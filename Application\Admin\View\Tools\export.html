<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">数据备份</span>
		</div>

		<div class="cf">
			<div class="fl">
				<a id="export" class="btn" href="javascript:;" autocomplete="off">立即备份</a>
				<a id="optimize" class="btn" href="{:U('Tools/optimize')}">优化表</a>
				<a id="repair" class="btn" href="{:U('Tools/repair')}">修复表</a>
			</div>
		</div>
		<div class="data-table table-striped">
			<form id="export-form" method="post" action="{:U('Tools/export')}">
				<table>
					<thead>
						<tr>
							<th width="48"><input class="check-all" type="checkbox" value="" checked="checked"></th>
							<th>表名</th>
							<th width="120">数据量</th>
							<th width="120">数据大小</th>
							<th width="160">创建时间</th>
							<th width="160">备份状态</th>
							<th width="">操作</th>
						</tr>
					</thead>
					<tbody>
						<volist name="list" id="table">
						<tr>
							<td class="num"><input class="ids" type="checkbox" name="tables[]" value="{$table.name}" checked="checked"></td>
							<td>{$table.name}</td>
							<td>{$table.rows}</td>
							<td>{$table.data_length|format_bytes}</td>
							<td>{$table.create_time}</td>
							<td class="info">未备份</td>
							<td class="action">
								<a class="no-refresh btn-success btn-xs" href="{:U('Tools/excel?tables='.$table['name'])}">导出表</a> |
								<a class="ajax-get no-refresh btn-info btn-xs" href="{:U('Tools/optimize?tables='.$table['name'])}">优化表</a> |
								<a class="ajax-get no-refresh btn-warning btn-xs" href="{:U('Tools/repair?tables='.$table['name'])}">修复表</a>
							</td>
						</tr>
						</volist>
					</tbody>
				</table>
			</form>
		</div>
	</div>
</div>
<script type="text/javascript">
	(function($){
		var $form=$("#export-form"),$export=$("#export"),tables
		$optimize=$("#optimize"),$repair=$("#repair");
		$optimize.add($repair).click(function(){
			$.post(this.href,$form.serialize(),function(data){
				if(data.status){
					updateAlert(data.info,'alert-success');
				}else{
					updateAlert(data.info,'alert-error');
				}
				setTimeout(function(){
					$('#top-alert').find('button').click();
					$(that).removeClass('disabled').prop('disabled',false);
				},1500);
			},"json");
			return false;
		});
		$export.click(function(){
			$export.parent().children().addClass("disabled");
			$export.html("正在发送备份请求...");
			$.post($form.attr("action"),$form.serialize(),function(data){
				if(data.status){
					tables=data.tables;
					$export.html(data.info+"开始备份，请不要关闭本页面！");
					backup(data.tab);
					window.onbeforeunload=function(){
						return "正在备份数据库，请不要关闭！"
					}
				}else{
					updateAlert(data.info,'alert-error');
					$export.parent().children().removeClass("disabled");
					$export.html("立即备份");
					setTimeout(function(){
						$('#top-alert').find('button').click();
						$(that).removeClass('disabled').prop('disabled',false);
					},1500);
				}
			},"json");
			return false;
		});
		function backup(tab,status){
			status&&showmsg(tab.id,"开始备份...(0%)");
			$.get($form.attr("action"),tab,function(data){
				if(data.status){
					//alert(tab.id + '|' +data.info);
					showmsg(tab.id,data.info);
					if(!$.isPlainObject(data.tab)){
						$export.parent().children().removeClass("disabled");
						$export.html("备份完成，点击重新备份");
						window.onbeforeunload=function(){
							return null
						}
						return;
					}
					backup(data.tab,tab.id!=data.tab.id);
				}else{
					updateAlert(data.info,'alert-error');
					$export.parent().children().removeClass("disabled");
					$export.html("立即备份");
					setTimeout(function(){
						$('#top-alert').find('button').click();
						$(that).removeClass('disabled').prop('disabled',false);
					},1500);
				}
			},"json");
		}
		function showmsg(id,msg){
			$form.find("input[value="+tables[id]+"]").closest("tr").find(".info").html(msg);
		}
	})(jQuery);
</script>
<script type="text/javascript">
	//主导航高亮
	$('.index-box').addClass('current');
	$('.index-export').addClass('current');
</script>
<include file="Public:footer" />