<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	   
	    <style>
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-1pysja1 {box-sizing: border-box;margin: 0;min-width: 0; -webkit-flex: 1;-ms-flex: 1;flex: 1;}
            .css-6nqu2s {box-sizing: border-box; margin: 0;min-width: 0;}
            .css-b22026 {box-sizing: border-box;margin: 0;min-width: 0;}
            .css-1xamyaw {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                /*margin-left: auto;*/
                margin-right: auto;
                max-width: 1200px;
                font-size: 12px;
                -webkit-flex-wrap: wrap;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                -webkit-flex-direction: row;
                -ms-flex-direction: row;
                flex-direction: row;
                margin-bottom: 0;
                padding-left: 16px;
                padding-right: 16px;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
            }
            .css-1xamyaw {
                margin-bottom: 0;
                padding-left: 24px;
                padding-right: 24px;
            }
            .css-o32gok {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 600;
                font-size: 28px;
                line-height: 36px;
                font-size: 24px;
                /*color: #1E2329;*/
                color: #FAFAFA;
            }
            .css-jwys36 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                display: none;
                margin-left: auto;
            }
            .css-8puh95 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-inline-box;
                display: -webkit-inline-flex;
                display: -ms-inline-flexbox;
                display: inline-flex;
                position: relative;
                height: 32px;
                margin-top: 4px;
                margin-bottom: 0px;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                line-height: 1.6;
                border: 1px solid transparent;
                border-color: #EAECEF;
                border-radius: 4px;
                padding-left: 8px;
                padding-right: 8px;
                background-color: #FFFFFF;
                margin-top: 0;
                width: 100%;
                height: 44px;
            }
            .css-1t9l9dt {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                /*background-color: #FAFAFA;*/
                padding-top: 24px;
                padding-bottom: 24px;
                padding-left: 16px;
                padding-right: 16px;
                display: none;
            }
            .css-1t9l9dt {
                padding-left: 24px;
                padding-right: 24px;
                display: block;
            }
            .css-8hstpq {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                margin-left: auto;
                margin-right: auto;
                max-width: 1200px;
                font-size: 12px;
                -webkit-flex-wrap: wrap;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                -webkit-flex-direction: row;
                -ms-flex-direction: row;
                flex-direction: row;
                margin-bottom: -16px;
                margin-left: 0;
            }
            .css-8hstpq {
                margin-bottom: 0;
                margin-left: auto;
            }
            .tophangqi{height:120px;width:22%;background-color:#f3f3f3;border-radius:10px;padding:0px 15px;}
            .css-194m5n4 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                background-color: #FFFFFF;
                /*padding-bottom: 24px;*/
                border-radius: 32px 32px 0px 0px;

            }
            .css-1hc8c4h {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                margin-left: auto;
                margin-right: auto;
                max-width: 1200px;
                padding-left: 0;
                padding-right: 0;
                border-radius: 32px 32px 0px 0px;
            }
            .css-1hc8c4h {
                padding-left: 8px;
                padding-right: 8px;
            }
            .css-dlistbox{width:100%;min-height:500px;}
            .css-dlbbox{width:100%;height:30px;background-color:#f3f3f3;padding:0px 10px;}
            .listtitle{height:30px;line-height:30px;}
            .css-dlbbox2{width:100%;height:50px;background-color:#fff;padding:0px 10px;border-bottom:1px solid #f3f3f3;}
            .listtitle2{height:50px;line-height:50px;}

			.bg-box {
				background:#fff;
				width: 100%;
				position: relative;
				height: 560px;
				padding-top: 158px;
				text-align: center;
			}

			.css-8hstpq_img {
				width: 100%;
				height: 120px;
			}

			.tophangqi {
				/*margin-right: 1%;*/
				width: 25%;
			}

			.reginput{
				width: 380px !important;
				height: 60px !important;
				border-radius: 10px !important;
			}

			.regbtn {
				width: 100px !important;
				height: 60px !important;
				background: #07c160 !important;
				border-radius: 10px !important;
				border: none;
				color: #fff;
				font-size: 18px;
			}

			.css-1hc8c4h {
				border: 1px solid #e9e2e2;
				border-radius: 0px;
				padding: 0px;
			}

			.regbtnimg {
				width: 720px;
				position: absolute;
				right: 0px;
				bottom: -120px;
				box-shadow: none !important;


			}

			.css-1xamyaw-content {
				width: 100%;
				color: #fff;
				text-align: center;
				padding-right: 100px;
			}

			.bg-box {
				padding-top: 0px;
    		}

			.reg-input-box {
				margin-top: 60px;
			}

			.option-box {
				width: 40%;
				height: 60%;
				background: #07c160;
				margin-top: 7%;
				margin: 7% auto;
				line-height: 30px;
				border-radius:3px;
				text-decoration :none !important;
			}

			.profit_loss_g {
				background: #eaf6f3;
				padding: 7px;
			}

			.profit_loss_r {
				background: #feeeee;
				padding: 7px;
			}

			.fred {
				color: #fa5252;
			}

			.rgreen {
				color: #12b886;
			}
			.body {
				background: #f6f7f9;
				background-color: #f6f7f9;
			}

			.img-fluid {
				width: 288px !important;
				height: 122px !important;
				border-radius: 5px;
			}

			.notice {
				height: 50px;
				width: 100%;
			}

			.box {
				width: 1000px;
				height: 50px;
				background-color: #fff;
				position: relative;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				/*margin: auto;*/
				overflow: hidden;
				color: #000;
				border-radius: 0px 10px 10px 0px;
			}
			.box ul {
				position: absolute;
				top: 0;
				left: 0;
			}
			.box ul li {
				line-height: 50px;
				list-style: none;
				padding: 0 30px;
				box-sizing: border-box;
				cursor: pointer;
			}

			.box_ul >li {
				padding-left: 0px;
			}





		</style>
		<link rel="stylesheet" href="/Public/Static/bootstrap5Slide/bootstrap.min.css">
		<link rel="stylesheet" href="/Public/Static/bootstrap5Slide/style.css">


	</head>
	<body style="background-color: #f6f7f9;">
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            
	            <main class="css-1wr4jig">

	                <div class="css-1pysja1">
						<div class="bg-box">
							<div class="css-1t9l9dt" style="width: 100%;padding-top:74px;">
							<div class="css-8hstpq-bg css-8hstpq" >
									<div class="css-b22026 reg-input-box">
									<div class="css-1xamyaw  css-1xamyaw-l">
										<div class="css-1xamyaw-content">
											<p  class="fw" style="font-size: 48px;padding-bottom: 40px;text-align: initial;color:#151617;">Find Your Next Moonshot</p>

											<p style="font-size: 26px;color:#444545;width: 500px;"> {:L('24小时交易量')} ${$indexhome}</p>
											<p style="font-size: 18px;color:#444545;width: 500px;height: 39px;"> </p>
										</div>
										<if condition="$uid elt 0">
										<form class="form-inline" action="{:U('Login/register')}" >
											<input type="email" class="form-control mb-2 mr-sm-2 reginput" placeholder="{:L('请输入邮箱或手机')}" id="email">
											<button type="submit" class=" mb-2 regbtn">{:L('注册')}</button>
										</form>
										</if>
									</div>

									<div class="css-1xamyaw  css-xamyaw-r">
<!--										<img src="/Public/Home/static/imgs/home_head_bg.png" class="regbtnimg" />-->
									</div>
								</div>
							</div>
							</div>

						</div>

						<div>
							<div class="css-1t9l9dt" style="width: 100%;background: #f6f7f9;padding: 55px 24px;">

								<div class="css-8hstpq" style="height: 220px;">
									<div style="
										width: 120px;
										margin-left: 40px;
										/*margin-right: 20px;*/
										height: 50px;
										background: #0052fe !important;
										float: right;
										text-align: center;
										line-height: 50px;
										font-size: 18px;
										color: #fff;
										border-radius: 10px 0px 0px 10px;
									">
										<p>{:L('公告')}: </p>
									</div>
									<div class="box">
										<ul id="box_ul">

										</ul>
									</div>

									<div class="container" >

										<div class="row mx-auto my-auto justify-content-center">
											<div id="recipeCarousel" class="carousel slide" data-bs-ride="carousel">
												<div class="carousel-inner" role="listbox">
													<div class="carousel-item active">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildea')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 1</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildeb')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 2</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsildec')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 3</div>-->
															</div>
														</div>
													</div>
													<div class="carousel-item">
														<div class="col-md-3">
															<div class="card">
																<div class="card-img">
																	<img src="/Upload/public/{:get_config('wapsilded')}" class="img-fluid">
																</div>
<!--																<div class="card-img-overlay">Slide 4</div>-->
															</div>
														</div>
													</div>
<!--													<div class="carousel-item">-->
<!--														<div class="col-md-3">-->
<!--															<div class="card">-->
<!--																<div class="card-img">-->
<!--																	<img src="https://via.placeholder.com/500x400/aba?text=5" class="img-fluid">-->
<!--																</div>-->
<!--																<div class="card-img-overlay">Slide 5</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->
<!--													<div class="carousel-item">-->
<!--														<div class="col-md-3">-->
<!--															<div class="card">-->
<!--																<div class="card-img">-->
<!--																	<img src="https://via.placeholder.com/500x400/fc0?text=6" class="img-fluid">-->
<!--																</div>-->
<!--																<div class="card-img-overlay">Slide 6</div>-->
<!--															</div>-->
<!--														</div>-->
<!--													</div>-->
												</div>
												<a class="carousel-control-prev bg-transparent w-aut" href="#recipeCarousel" role="button" data-bs-slide="prev">
													<span class="carousel-control-prev-icon" aria-hidden="true"></span>
												</a>
												<a class="carousel-control-next bg-transparent w-aut" href="#recipeCarousel" role="button" data-bs-slide="next">
													<span class="carousel-control-next-icon" aria-hidden="true"></span>
												</a>
											</div>
										</div>

									</div>


								</div>

							</div>
						</div>
	                	<div class="css-194m5n4" style="padding-top:30px;">
	                    <div class="css-1hc8c4h">
	<?php $MBN1= M("issue")->where(["id"=>3])->find(); ?>
	                        <div class="css-dlistbox" >
	                            <div class="css-dlbbox">
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('名称')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('价格')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('涨跌幅')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('最高')}/{:L('最低')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">24H{:L('量')}</span>
	                                </div>
	                                <div class="listtitle tcc fl col-2">
	                                    <span class="f12 fch">{:L('操作')}</span>
	                                </div>
	                            </div>
	                              <div class="css-dlbbox2 market-div" >
	                                <div class="listtitle2 tcl fl col-2" >
										<img src="/xm/mbn.png" class="cion_logo">
	                                    <span class="f14 fch fw cn_mbn">MORBION</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fgreen fw cpr_mbn">{$MBN1.price}</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2 cch_mbn" >
											<span class="f14 fgreen profit_loss_g fw">{$MBN1.lixi}%</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw hl_mbn">{$MBN1.price}/{$MBN1.yuan_price}</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw vol_mbn">{$MBN1.sellnum}</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
									
	                                    <div class="tcc option-box" style="width:60%;float:center;">
	                                     <a href="/Issue/details?id=3" href="" class="f14 " style="color: #fff;text-decoration:none">{:L('立即参与')}</a>
										</div>
	                                </div>
	                            </div>
	                            <foreach name="market" item="vo">
	                            <div class="css-dlbbox2 market-div" >
	                                <div class="listtitle2 tcl fl col-2" >
										<img src="{$vo.logo}" class="cion_logo">
	                                    <span class="f14 fch fw cn_{$vo.coinname}"><?php echo strtoupper($vo['coinname']);?>/USDT</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch cpr_{$vo.coinname}">--:--</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2 cch_{$vo.coinname}" >
											<span class="f14 fch fw profit_loss">0.00%</span>
	                                </div>

	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw hl_{$vo.coinname}">--.--/--.--</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
	                                    <span class="f14 fch fw vol_{$vo.coinname}">--.--</span>
	                                </div>
	                                <div class="listtitle2 tcc fl col-2" >
										<div class="tcc option-box" style="float:right;">
	                                    <a href="{:U('Trade/index')}?type=buy&symbol=<?php echo strtoupper($vo['coinname']);?>" href="" class="f14 " style="color: #fff;text-decoration:none">{:L('交易')}</a>	</div>
	                                    <div class="tcc option-box" style="float:left;">
	                                     <a href="{:U('Contract/index')}?coin=<?php echo strtoupper($vo['coinname']);?>" href="" class="f14 " style="color: #fff;text-decoration:none">{:L('合约')}</a>
										</div>
	                                </div>
	                            </div>
	                            </foreach>

	                        </div>

	                    </div>

						<div class="css-1hc8c4h index-box-2" >
								<div class="css-dlistbox" style="min-height: 800px;" >
									<!-- 顶部显示-->
									<div class="css-dlistbox-top css-dlistbox-sub" >
										<div class="ss-dlistbox-top-text">
											<p class="ss-dlistbox-top-p1" >{:L('立即开始您的加密货币之旅')}</p>
											<p class="ss-dlistbox-top-p2">{:get_config('webname')} {:L('全球公司拥有多种功能，使其成为买卖数字资产的理想场所')}</p>
										</div>

									</div>
									<!-- 顶部显示-->
									<!-- 左侧-->
									<div class="css-dlistbox-l css-dlistbox-sub" >
										<div class="css-dlistbox-l-item1">
											<div class="css-dlistbox-l-content">
												<div class="icon1-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('管理您的资产')}</p>
												<p>{:L('以高达5倍的杠杆率进行现货交易')}</p>
											</div>
											<div class="css-dlistbox-l-content">
												<div class="icon2-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('信用卡付款')}</p>
												<p>{:L('通过我们的合作伙伴用您的信用卡购买加密货币')}</p>
											</div>
											<div class="css-dlistbox-l-content css-dlistbox-l-content-4">
												<div class="icon3-73beb614 icon-73beb614"></div>
												<p class="data-p-title">{:L('安全储存')}</p>
												<p class="data-p-content">{:L('客户资金存放在专用的多签名')}</p>
												<p class="data-p-content">{:L('冷钱包中.24/7全天候安全监控')}</p>
												<p class="data-p-content">{:L('专用20,000 BTC安全储备金')}</b></p>
											</div>
											<div class="css-dlistbox-l-content">
												<div class="icon-73beb614"></div>
												<p class="data-p-title">{:L('随处访问')}</p>
												<p class="data-p-content">{:L('在我们用于Android和iOS的移动应用上进行24/7全')}</p>
												<p class="data-p-content">{:L('天候存款，取款和交易')}</p>
											</div>

											<div class="css-dlistbox-l-download css-dlistbox-l-content">
												<div class="icon-73beb614"></div>
												<div class="ios-down-73beb614"></div>
												<div class="android-down-73beb614"></div>
											</div>
										</div>

									</div>
									<!-- 左侧-->
									<!-- 右侧-->
									<div class="css-dlistbox-r css-dlistbox-sub" >
										<div class="css-dlistbox-bg">

										</div>
										<div class="css-dlistbox-phone">

										</div>
										<div class="img1">

										</div>
										<div class="img2">

										</div>
									</div>
									<!-- 右侧-->
								</div>



							</div>

						<div class="css-1hc8c4h index-box-3">
							<div class="css-dlistbox" style="min-height: 300px" >
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub" >
									<div class="css-dlistbox-l-item1">
										<div class="">
											<p class="ss-dlistbox-top-p1 tcl" >The most complete trading cryptocurrency platform</p>
											<p class="ss-dlistbox-top-p2 tcl">Here are a few reasons why you should choose {:get_config('webname')} </p>
										</div>
									</div>
								</div>
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub-desc" >
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_margin.svg" class="home_margin__qse_K" alt="">
												<p>Maximize profit with leverage</p>
											</div>
										</div>
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_contract.svg" class="home_margin__qse_K" alt="">
												<p>Up to 125x leverage with superior spreads</p>
											</div>
										</div>
										<div class="css-dlistbox-desc-box col-4">
											<div class="home_infoWrapper__G_KFW">
												<img src="/Public/Home/static/imgs/icon_etf.svg" class="home_margin__qse_K" alt="">
												<p>Increased leverage, no liquidation risk</p>
											</div>
										</div>

								</div>

							</div>
						</div>

						<div class="index-box-4">
							<div class="css-1hc8c4h css-1hc8c4h-box-4 st">
								<div class="css-dlistbox css-dlistbox-4" >
								<!-- 顶部显示-->
								<div class="css-dlistbox-top-desc css-dlistbox-sub" >
									<div class="css-dlistbox-l-item1">
										<div class="">
											<p class="ss-dlistbox-top-p1 tcl" >Join the {:get_config('webname')} Community Today</p>
											<p class="ss-dlistbox-top-p2 tcl">Always there for you</p>
										</div>
									</div>
								</div>
								<if condition="$uid elt 0">
								<div class="css-1xamyaw  css-1xamyaw-l" style="padding: 20px 20px 20px 20px;">
									<form class="form-inline" action="{:U('Login/register')}" >
										<input type="email" class="form-control mb-2 mr-sm-2 reginput" placeholder="{:L('请输入邮箱或手机')}" id="email">
										<button type="submit" class=" mb-2 regbtn">{:L('注册')}</button>
									</form>
								</div>
								</if>
								



							</div>
							</div>
						</div>


	                </div>
					</div>
	            </main>
	            
	            
	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>
    <script type="text/javascript">
        function obtain_btc(){
            var coin = "btc";
            var nameclass = ".cn_btc";
            var priceclass = ".cpr_btc";
            var pricehl = ".hl_btc";
            var pricevol = ".vol_btc";
            var changeclass = ".cch_btc";
            $.post("{:U('Ajaxtrade/obtain_btc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_eth(){
            var coin = "eth";
            var nameclass = ".cn_eth";
            var priceclass = ".cpr_eth";
            var pricehl = ".hl_eth";
            var pricevol = ".vol_eth";
            var changeclass = ".cch_eth";
            $.post("{:U('Ajaxtrade/obtain_eth')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_eos(){
            var coin = "eos";
            var nameclass = ".cn_eos";
            var priceclass = ".cpr_eos";
            var pricehl = ".hl_eos";
            var pricevol = ".vol_eos";
            var changeclass = ".cch_eos";
            $.post("{:U('Ajaxtrade/obtain_eos')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_doge(){
            var coin = "doge";
            var nameclass = ".cn_doge";
            var priceclass = ".cpr_doge";
            var pricehl = ".hl_doge";
            var pricevol = ".vol_doge";
            var changeclass = ".cch_doge";
            $.post("{:U('Ajaxtrade/obtain_doge')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_bch(){
            var coin = "bch";
            var nameclass = ".cn_bch";
            var priceclass = ".cpr_bch";
            var pricehl = ".hl_bch";
            var pricevol = ".vol_bch";
            var changeclass = ".cch_bch";
            $.post("{:U('Ajaxtrade/obtain_bch')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_ltc(){
            var coin = "ltc";
            var nameclass = ".cn_ltc";
            var priceclass = ".cpr_ltc";
            var pricehl = ".hl_ltc";
            var pricevol = ".vol_ltc";
            var changeclass = ".cch_ltc";
            $.post("{:U('Ajaxtrade/obtain_ltc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_iota(){
            var coin = "iota";
            var nameclass = ".cn_iota";
            var priceclass = ".cpr_iota";
            var pricehl = ".hl_iota";
            var pricevol = ".vol_iota";
            var changeclass = ".cch_iota";
            $.post("{:U('Ajaxtrade/obtain_iota')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_fil(){
            var coin = "fil";
            var nameclass = ".cn_fil";
            var priceclass = ".cpr_fil";
            var pricehl = ".hl_fil";
            var pricevol = ".vol_fil";
            var changeclass = ".cch_fil";
            $.post("{:U('Ajaxtrade/obtain_fil')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_flow(){
            var coin = "flow";
            var nameclass = ".cn_flow";
            var priceclass = ".cpr_flow";
            var pricehl = ".hl_flow";
            var pricevol = ".vol_flow";
            var changeclass = ".cch_flow";
            $.post("{:U('Ajaxtrade/obtain_flow')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_jst(){
            var coin = "jst";
            var nameclass = ".cn_jst";
            var priceclass = ".cpr_jst";
            var pricehl = ".hl_jst";
            var pricevol = ".vol_jst";
            var changeclass = ".cch_jst";
            $.post("{:U('Ajaxtrade/obtain_jst')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_itc(){
            var coin = "itc";
            var nameclass = ".cn_itc";
            var priceclass = ".cpr_itc";
            var pricehl = ".hl_itc";
            var pricevol = ".vol_itc";
            var changeclass = ".cch_itc";
            $.post("{:U('Ajaxtrade/obtain_itc')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_ht(){
            var coin = "ht";
            var nameclass = ".cn_ht";
            var priceclass = ".cpr_ht";
            var pricehl = ".hl_ht";
            var pricevol = ".vol_ht";
            var changeclass = ".cch_ht";
            $.post("{:U('Ajaxtrade/obtain_ht')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
        function obtain_usdz(){
            var coin = "usdz";
            var nameclass = ".cn_usdz";
            var priceclass = ".cpr_usdz";
            var pricehl = ".hl_usdz";
            var pricevol = ".vol_usdz";
            var changeclass = ".cch_usdz";
            $.post("{:U('Ajaxtrade/obtain_usdz')}",
            {'coin':coin},
            function(data){
                if(data.code == 1){
                    $(nameclass).html(data.cname);
                    $(priceclass).html(data.open);
                    $(pricehl).html(data.highlow);
                    $(pricevol).html(data.amount);
                    $(changeclass).html(data.change);
                }else{
                    console.log(data.info);return false;
                }
            }
            );
        }
    </script>
    <script type="text/javascript">
        let hburl = "wss://api.huobi.pro/ws";  // 实时币种价格
        let requestK = { // 请求对应信息的数据
         "sub": "market.btcusdt.kline.1day"
        };
        
        let subK = {
         "sub": "market.ethusdt.kline.1day"
        };
        let subK1 = {
         "sub": "market.bchusdt.kline.1day"
        };
        
        let subK2 = {
         "sub": "market.eosusdt.kline.1day"
        };
        let subK3 = {
         "sub": "market.dogeusdt.kline.1day"
        };
        let subK4 = {
         "sub": "market.ltcusdt.kline.1day"
        };
    
    
        
        
        let socketK = new WebSocket(hburl);
        socketK.onopen = function () {
            console.log("connection establish");
            socketK.send(JSON.stringify(subK));
            socketK.send(JSON.stringify(requestK));
            socketK.send(JSON.stringify(subK1));
            socketK.send(JSON.stringify(subK2));
            socketK.send(JSON.stringify(subK3));
            socketK.send(JSON.stringify(subK4));
        };
        socketK.onmessage = function (event) {
            let blob = event.data;
            let reader = new FileReader();
            reader.onload = function (e) {
                let ploydata = new Uint8Array(e.target.result);
                let msg = pako.inflate(ploydata, {to: "string"});
                handleData(msg);
            };
            reader.readAsArrayBuffer(blob, "utf-8");
        };
        socketK.onclose = function () {
            console.log("connection closed");
        };
        $(function(){
              //obtain_btc();
            // setInterval("obtain_btc()",2000); 
            //obtain_eth();
            // setInterval("obtain_eth()",3000); 
            // //obtain_eos();
            // setInterval("obtain_eos()",5000);
            // obtain_doge();
            // setInterval("obtain_doge()",7000);
            // obtain_bch();
            // setInterval("obtain_bch()",9000);
            // obtain_ltc();
            // setInterval("obtain_ltc()",11000);
            
            // obtain_iota();
            // setInterval("obtain_iota()",13000);
            
            // obtain_fil();
            // setInterval("obtain_fil()",15000);
            
            // obtain_flow();
            // setInterval("obtain_flow()",17000);
            
            // obtain_jst();
            // setInterval("obtain_jst()",19000);
            
            // obtain_itc();
            // setInterval("obtain_itc()",21000);
            
            // obtain_ht();
            // setInterval("obtain_ht()",23000);
            
            // obtain_usdz();
            // setInterval("obtain_usdz()",25000);
        });
    </script>
    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>


	<script src="/Public/Static/bootstrap5Slide/bootstrap.bundle.min.js"></script>
	<script src="/Public/Static/bootstrap5Slide/scripts.js" type="text/javascript"></script>

	<script>
		//消息内容，可以任意长度
		let arr = ["announcement1 : announcement", "announcement2 : announcement2", "announcement3 : announcement3"];

		var settings = {
			speeds: 10, //滚动的速度,单位ms
			isPause: true, //滚动后每个消息是否需要暂停，true和false,
			isHover:true, //鼠标悬停是否需要暂停
		};
		var ul = $('#box_ul')[0];
		//渲染数据
		arr.forEach((item) => {
			var li = document.createElement("li");
			li.innerHTML = item;
			ul.appendChild(li);
		});
		//获取当前滚动的高度，支持ie请自行使用currentStyle
		var currentTop = parseInt(window.getComputedStyle(ul).top);

		//滚动函数
		function run() {
			currentTop--;
			ul.style.top = currentTop + "px";

			//当页面滚动最后一个时，把第一个复制push到尾部
			if (currentTop == (arr.length - 1) * -50) {
				let li = document.createElement("li");
				li.innerHTML = arr[0];
				ul.appendChild(li);
			}

			//无缝替换
			if (currentTop == arr.length * -50) {
				currentTop = 0;
				ul.style.top = currentTop + "px";
				var li = document.querySelectorAll("li");
				ul.removeChild(li[li.length - 1]);
			}

			//滚动后每个消息是否需要暂停
			if (settings.isPause) {
				if (currentTop % 50 == 0) {
					clearInterval(timer);
					setTimeout(function () {
						timer = setInterval(run, settings.speeds);
					}, 3000);
				}
			}
		}
		var timer = setInterval(run, settings.speeds);

		//鼠标悬停是否需要暂停
		ul.onmouseover = function () {
			if(settings.isHover){
				clearInterval(timer);
			}
		};
		ul.onmouseleave = function () {
			clearInterval(timer);
			if(settings.isHover){
				timer = setInterval(run, settings.speeds);
			}
		};

	</script>

	<script>

		$('.market-div').hover(
				function () {
					$(this).css('transform', 'scale(1.02)');
					$(this).css('background', '#f3f3f3');
				},
				function () {
					$(this).css('transform', 'scale(1)');
					$(this).css('background', '#fff');

				}
		)
	</script>
  <script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script src="https://cdn.staticfile.org/pako/1.0.10/pako.min.js"></script>  
<script>
// 处理接收到的信息
function handleData(msg) {
    let data = JSON.parse(msg);
    if (data.ping) {
        // 如果是 ping 消息
        sendHeartMessage(data.ping);
    } else if (data.status === "ok") {
        // 响应数据
        handleReponseData(data);
    } else {
        // 数据体
        // console.log(data)
        if(data.ch=='market.ethusdt.kline.1day'){
         
            
                //波动btc
                $('.cn_eth').html('ETH/USDT');
                $("#eth_coin_box").html('ETH/USDT');
                
                
                $('.hl_eth').html("<span  class='f14 fch fw hl_bch'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_eth').html("<span  class='f14 fch fw vol_bch'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_eth').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_eth').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_eth').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_eth').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                }             
        }else if(data.ch=='market.btcusdt.kline.1day'){
         
            
                //波动btc
                $('.cn_btc').html('BTC/USDT');
                $("#btc_coin_box").html('BTC/USDT');
                
                
                $('.hl_btc').html("<span  class='f14 fch fw hl_btc'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_btc').html("<span  class='f14 fch fw vol_btc'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_btc').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_btc').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_btc').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_btc').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                }            
        }else if(data.ch=='market.bchusdt.kline.1day'){
            
                //波动btc
                $('.cn_bch').html('BCH/USDT');
                
                
                $('.hl_bch').html("<span  class='f14 fch fw hl_bch'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_bch').html("<span  class='f14 fch fw vol_bch'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_bch').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_bch').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_bch').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_bch').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                } 
            
            
            
            
        }else if(data.ch=='market.eosusdt.kline.1day'){
                $('.cn_eos').html('EOS/USDT');
                
                
                $('.hl_eos').html("<span  class='f14 fch fw hl_eos'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_eos').html("<span  class='f14 fch fw vol_eos'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_eos').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_eos').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_eos').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_eos').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                }           
            
            
            
            
            
        }else if(data.ch=='market.dogeusdt.kline.1day'){
                $('.cn_doge').html('DOGE/USDT');
                
                
                $('.hl_doge').html("<span  class='f14 fch fw hl_doge'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_doge').html("<span  class='f14 fch fw vol_doge'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_doge').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_doge').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_doge').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_doge').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                }           
            
            
            
            
            
        }else if(data.ch=='market.ltcusdt.kline.1day'){
                $('.cn_ltc').html('LTC/USDT');
                
                
                $('.hl_ltc').html("<span  class='f14 fch fw hl_ltc'>"+data.tick.high+'/'+data.tick.low+"</span>");
                $('.vol_ltc').html("<span  class='f14 fch fw vol_ltc'>"+data.tick.amount+"</span>");
                if(data.tick.close>data.tick.open){
                 
                    
                    $('.cpr_ltc').html("<span  class='f14 fgreen fw'>"+data.tick.close+"</span>");
                }else{
                  
                     $('.cpr_ltc').html("<span  class='f14 fred fw'>"+data.tick.close+"</span>");
                }
                var fd=data.tick.close-data.tick.open;
                var fd2=(fd/data.tick.open*100);
                if(fd2>=0){
                  
                    $('.cch_ltc').html("<span  class='f14 fgreen profit_loss_g fw'>"+fd2.toFixed(2)+"%</span>");
                }else{
               
                    $('.cch_ltc').html("<span  class='f14 fred profit_loss_r fw'>"+fd2.toFixed(2)+"%</span>");
                }   
            
            
            
            
        }
        
        
        
        
        
    }
}
    // 发送响应信息
    function sendHeartMessage(ping) {
        socketK.send(JSON.stringify({"pong": ping}));
    }
    
    function handleReponseData(data) {
    }

    // 解压
    function unzip(b64Data) {
        let strData = atob(b64Data);
        const charData = strData.split('').map(function (x) {
            return x.charCodeAt(0);
        });
        const binData = new Uint8Array(charData);
        const data = pako.inflate(binData);
        strData = String.fromCharCode.apply(null, new Uint16Array(data));
        return decodeURIComponent(strData);
    }

    // 压缩
    function zip(str) {
        const binaryString = pako.gzip(encodeURIComponent(str), {to: 'string'})
        return btoa(binaryString);
    }
</script>
</html>