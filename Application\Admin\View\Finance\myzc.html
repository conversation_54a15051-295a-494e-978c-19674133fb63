<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">数字币提币记录</span>
            <div class="fr">
                <button class="btn btn-warning" onClick="location.href='{:U('Finance/myzc')}'">初始化搜索</button>
            </div>
        </div>

        <div class="cf">
            
            <div class="search-form fr cf" style="float: none !important;">
                <div class="sleft">
                    <form name="formSearch" id="formSearch" method="get" name="form1">
                        <select style=" width: 100px; float: left; margin-right: 10px;" name="field" class="form-control">
                            <option value="username"
                            <eq name="Think.get.field" value="username">selected</eq>
                            >用户名</option>
                        </select>
                        								    
<label for="statrtime"  style=" width: 250px; float: left; margin-right: 10px;" >开始时间：<input id="statrtime" name="statrtime" type="date" value="<?php if($_GET['statrtime']){echo $_GET['statrtime'];}else{ ?>date<?php } ?>"/></label>
<label for="endtime"  style=" width: 250px; float: left; margin-right: 10px;" >结束时间：<input id="endtime" name="endtime" type="date" value="<?php if($_GET['endtime']){echo $_GET['endtime'];}else{ ?>date<?php } ?>"/></label>
                        <input type="text" name="name" class="search-input form-control  " value="{$Think.get.name}" placeholder="请输入查询内容" style="">
                        <a class="sch-btn" href="javascript:;" id="search"> <i class="btn-search"></i> </a>
                    </form>
                    <script>
                        //搜索功能
                        $(function () {
                            $('#search').click(function () {
                                $('#formSearch').submit();
                            });
                        });
                        //回车搜索
                        $(".search-input").keyup(function (e) {
                            if (e.keyCode === 13) {
                                $("#search").click();
                                return false;
                            }
                        });
                    </script>
                </div>
            </div>
        </div>

        <div class="data-table table-striped">
            <table class="">
                <thead>
                <tr>
                    <th class="">ID</th>
                    <th class="">用户名</th>
                    <th class="">币种名称</th>
                    <th class="">提现网络</th>
                    <th width="">申请时间</th>
                    <th width="">审核时间</th>
                    <th width="">提币地址</th>
                    <th width="">提币数量</th>
                    <th class="">提现手续费</th>
                    <th class="">实际到账</th>
                    <th width="">状态</th>
                    <th width="">操作</th>
                </tr>
                </thead>
                <tbody>
                <notempty name="list">
                    <volist name="list" id="vo">
                        <tr>
                            <td>{$vo.id}</td>
                            <td>{$vo.username} </td>
                            <td><?php echo strtoupper($vo['coinname']);?></td>
                            <td><?php echo strtoupper($vo['czline']);?></td>
                            <td>{$vo.addtime}</td>
                            <td>{$vo.endtime}</td>
                            <td>{$vo.address}</td>
                            <td>{$vo.num}</td>
                            <td>{$vo.fee}</td>
                            <td>{$vo.mum}</td>
                            <td>
                                <eq name="vo.status" value="1"><span style="color:blue;">待审核</span></eq>
                                <eq name="vo.status" value="2"><span style="color:green;">完成</span></eq>
                                <eq name="vo.status" value="3"><span style="color:red;">未通过</span></eq>
                            </td>
                            <td>
                                <eq name="vo.status" value="1">
                                    <input type="button" class="ajax-get btn btn-primary btn-xs" value="确认提币" onclick="Upzc('{$vo['id']}');"/>
                                    <input type="button" class="ajax-get btn btn-primary btn-xs" value="驳回提币" onclick="Upbh('{$vo['id']}');"/>
                                </eq>
                                <eq name="vo.status" value="2"><span style="color:blue;">已处理</span></eq>
                                <eq name="vo.status" value="3"><span style="color:blue;">已处理</span></eq>
                            </td>
                        </tr>
                    </volist>
                    <else/>
                    <td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
                </notempty>
                </tbody>
            </table>
            <div class="page">
                <div>{$page}</div>
            </div>
        </div>
    </div>
</div>
<include file="Public:footer"/>
<script type="text/javascript">
    function Upbh(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/reject')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function Upzc(id) {
        var zcid = parseInt(id);
        if (zcid == "" || zcid == null || zcid <=0) {
            layer.alert('参数错误！');
            return false;
        }
        layer.load(0, {shade: [0.5,'#8F8F8F']});
        $.post("{:U('Finance/adopttb')}", {
            id: zcid
        }, function (data) {
            setTimeout("closetanchu()",2000);
            if (data.status == 1) {
                layer.msg(data.info, {
                    icon: 1
                });
                setTimeout("shuaxin()",1000);
            } else {
                layer.msg(data.info, {
                    icon: 2
                });
            }
        }, "json");
    }
</script>
<script type="text/javascript">
    function closetanchu(){
        layer.closeAll('loading');
    }
    function shuaxin(){
        window.location.href=window.location.href;
    }
</script>

<script type="text/javascript">
    function showid(id){
        layer.open({
            type:1,
            skin:'layui-layer-rim', //加上边框
            area:['800px','100px'], //宽高
            title:'交易ID', //不显示标题
            closeBtn: 0,
            shadeClose: true,
            content:id
        });
    }
    //提交表单
    $('#submit').click(function () {
        $('#form').submit();
    });
</script>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('Finance/myzc')}");
    </script>
</block>