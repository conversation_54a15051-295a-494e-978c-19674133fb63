<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('User/admin')}">管理员管理</a> &gt;&gt;</span>
             <span class="h1-title"><empty name="data">添加管理员<else/>编辑管理员</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('User/adminEdit')}" method="post" class="form-horizontal">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>
								<tr class="controls">
									<td class="item-label">用户名 :</td>
									<td>
										<input type="text" class="form-control input-10x" name="username" value="{$data.username}">
									</td>
									<td class="item-note">* 长度5~15位</td>
								</tr>
								<tr class="controls">
									<td class="item-label">昵称 :</td>
									<td>
										<input type="text" class="form-control input-10x" name="nickname" value="{$data.nickname}">
									</td>
									<td class="item-note">备注</td>
								</tr>
								<tr class="controls">
									<td class="item-label">登录密码 :</td>
									<td><input type="password" class="form-control input-10x" name="password" value=""></td>
									<td class="item-note">留空不更新</td>
								</tr>
								<tr class="controls">
									<td class="item-label">手机 :</td>
									<td><input type="text" class="form-control input-10x" name="moble" value="{$data.moble}"></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">邮箱 :</td>
									<td><input type="text" class="form-control input-10x" name="email" value="{$data.email}"></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">状态 :</td>
									<td>
										<select name="status" class="form-control input-10x">
											<option value="1" <eq name="data.status" value="1">selected</eq>>正常</option>
											<option value="0" <eq name="data.status" value="0">selected</eq>>冻结</option>
										</select>
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">提交</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/></notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//提交表单
	$('#submit').click(function () {
		$('#form').submit();
	});
</script>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/admin')}");
	</script>
</block>