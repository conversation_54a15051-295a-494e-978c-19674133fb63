<include file="Public:header"/>
<script type="text/javascript" src="__PUBLIC__/layer/laydate/laydate.js"></script>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('User/index')}">用户管理</a> &gt;&gt;</span>
			<span class="h1-title"><empty name="data">添加用户<else/>编辑用户</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('User/edit')}" method="post" class="form-horizontal">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>

								<tr class="controls">
									<td class="item-label">用户名 :</td>
									<td>
										<input type="text" class="form-control input-10x" name="username" value="{$data.username}">
									</td>
									<td class="item-note"></td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">手机号 :</td>
									<td>
										<input type="text" class="form-control input-10x" name="phone" value="{$data.phone}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">用户密码 :</td>
									<td><input type="text" class="form-control input-10x" name="password" value=""></td>
									<td class="item-note" style="color:red;">*留空不更新</td>
								</tr>
								<tr class="controls">
									<td class="item-label">交易密码 :</td>
									<td><input type="text" class="form-control input-10x" name="paypassword" value="">
									</td>
									<td class="item-note" style="color:red;">*留空不更新</td>
								</tr>
								
								<empty name="data">
								<tr class="controls">
									<td class="item-label">邀请码 :</td>
									<td><input type="text" class="form-control input-10x" name="invit" value="">
									</td>
									<td class="item-note" style="color:red;">*上级邀请码</td>
								</tr>
								</empty>
								
								<tr class="controls">
									<td class="item-label">登陆状态 :</td>
									<td>
									    <select name="status" class="form-control input-10x">
										<option value="1" <eq name="data.status" value="1">selected</eq>>正常</option>
										<option value="2" <eq name="data.status" value="2">selected</eq>>冻结</option>
									    </select>
									</td>
									<td class="item-note" style="color:red;">*会员登陆的状态</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">提币状态 :</td>
									<td>
									    <select name="txstate" class="form-control input-10x">
										<option value="1" <eq name="data.txstate" value="1">selected</eq>>正常</option>
										<option value="2" <eq name="data.txstate" value="2">selected</eq>>禁止</option>
									</select></td>
									<td class="item-note" style="color:red;">*会员提币的状态</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">用户类型 :</td>
									<td>
									    <select name="user_type" class="form-control input-10x">
										<option value="1" <eq name="data.user_type" value="1">selected</eq>>正常用户</option>
										<option value="2" <eq name="data.user_type" value="2">selected</eq>>测试用户</option>
									</select></td>
									<td class="item-note" style="color:red;">*用户是系统测试用户还是正常用户</td>
								</tr>
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">提交</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//提交表单
	$('#submit').click(function () {
		$('#form').submit();
	});
</script>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('User/index')}");
	</script>
</block>