<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	    <style>
	        .css-155nz97 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                min-height: 100vh;
                color: #1E2329;
                background-color: #FFFFFF;
            }
            .css-1b9spv1 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: contents;
            }
            .css-qf37yy {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                background-color: #FFFFFF;
                height: 64px;
                width: 100%;
                padding-left: 16px;
                padding-right: 16px;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
            }
            .css-1en9dhb {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                color: #1E2329;
                overflow: hidden;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                pointer-events: none;
                visibility: hidden;
            }
            .css-1en9dhb {
                pointer-events: auto;
                visibility: visible;
            }
            .css-qf37yy >div {
                height: 100%;
            }
            .css-11y6cix {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-wu6zme {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex: none;
                -ms-flex: none;
                flex: none;
            }
             .css-143enlu {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                color: #1E2329;
                font-size: 14px;
                padding-left: 8px;
                padding-right: 8px;
            }
            .css-wu6zme >div {
                height: 100%;
            }
            .css-1ka9xaf {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                margin-top: 0;
                overflow: hidden;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
            }
            .css-8t6vxp {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                width: 100%;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                margin-top: 40px;
            }
            .css-16kn2us {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                max-width: 1200px;
                width: 100%;
            }
            .binance-row {
                display: flex;
                -webkit-box-orient: horizontal;
                -webkit-box-direction: normal;
                flex-flow: row wrap;
            }

            .binance-col-4 {
                display: block;
                flex: 0 0 16.6667%;
                max-width: 16.6667%;
            }
            
            .binance-col {
                position: relative;
                max-width: 100%;
                min-height: 1px;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
            }
            .binance-col {
                position: relative;
                max-width: 100%;
                min-height: 1px;
            }
            .css-1wz0uwi {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-align-items: flex-start;
                -webkit-box-align: flex-start;
                -ms-flex-align: flex-start;
                align-items: flex-start;
            }
            .css-9vx3s2 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                padding-left: 0px;
                padding-right: 0px;
                text-align: left;
                width: 100%;
                margin-bottom: 40px;
            }
            .css-1g5tc38 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 600;
                font-size: 32px;
                line-height: 40px;
                color: #1E2329;
                margin-bottom: 16px;
            }
            .css-152kxht {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                color: #474D57;
            }
            .css-128p7pv {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                width: 100%;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                padding-left: 0;
            }
            .css-tmpver {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                width: 100%;
                padding-left: 0px;
                padding-right: 0px;
            }
            .css-hlfj64 {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
            }
            .css-15651n7 {
                box-sizing: border-box;
                margin: 0px 0px 24px;
                min-width: 0px;
                width: 100%;
            }
            .css-xjlny9 {
                box-sizing: border-box;
                margin: 0px 0px 4px;
                min-width: 0px;
                display: flex;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                width: 100%;
                -webkit-box-align: center;
                align-items: center;
                color: rgb(30, 35, 41);
                cursor: auto;
            }
            .css-hiy16i {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                width: 100%;
                position: relative;
                min-height: 12px;
            }
            .css-13xytqh {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                display: inline-flex;
                position: relative;
                -webkit-box-align: center;
                align-items: center;
                line-height: 1.6;
                border: 1px solid rgb(234, 236, 239);
                border-radius: 4px;
                height: 48px;
                background-color: transparent;
                width: 100%;
            }
            .css-13xytqh input {
                color: rgb(30, 35, 41);
                font-size: 14px;
                padding-left: 12px;
                padding-right: 12px;
            }
            .css-16fg16t {
                box-sizing: border-box;
                margin: 0px;
                min-width: 0px;
                width: 100%;
                height: 100%;
                padding: 0px;
                outline: none;
                border: none;
                background-color: inherit;
                opacity: 1;
            }
            .css-13xytqh .bn-input-suffix {
                flex-shrink: 0;
                margin-left: 4px;
                margin-right: 4px;
                font-size: 14px;
            }
            .css-hlfj64 .bn-input-suffix {
                margin-right: 0px;
                line-height: 0.9;
            }
            .css-vurnku {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
            }
            .css-tmpver {
                width: 384px;
            }
            button, input {
                overflow: visible;
            }
            button, input, optgroup, select, textarea {
                font-family: inherit;
                font-size: 100%;
                line-height: 1.15;
                margin: 0px;
            }
            input:focus{background:#fff!important;outline: 1px solid rgb(252, 213, 53);}
            .css-1tsxga3 {
                margin: 0px;
                appearance: none;
                user-select: none;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: center;
                justify-content: center;
                box-sizing: border-box;
                font-family: inherit;
                text-align: center;
                text-decoration: none;
                outline: none;
                font-weight: 500;
                font-size: 16px;
                line-height: 24px;
                word-break: keep-all;
                color: rgb(33, 40, 51);
                border-radius: 6px;
                padding: 12px 24px;
                min-height: 24px;
                border: none;
                background-image: none;
                background-color: rgb(252, 213, 53);
                min-width: 80px;
                width: 100%;
            }
            .css-1pxm4lx {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                margin-top: 24px;
            }
            .css-s84t59 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                cursor: pointer;
                color: #C99400;
            }
            .css-utqtyo {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                cursor: pointer;
                display: block;
                margin-top: 8px;
                color: #C99400;
            }
            .css-s84t59:hover {
                -webkit-text-decoration: underline;
                text-decoration: underline;
                color: #FCD535;
            }
	    </style>
	</head>
    <body>
        <div class="App">
            <div class="css-155nz97" >
                <div class="hidden-header-in-bnc css-1b9spv1">
                    <header class="css-qf37yy" style="background-color:#f5f5f5;">
                        <a href="{:U('Index/index')}" clss="css-1mvf8us">
	                        <img src="/Public/Home/static/imgs/alogn.png" class="css-1jgk2rg" style="height:50px;width:50px;" />
	                    </a>
	                    <div class="css-1en9dhb"></div>
	                    <div class="css-11y6cix"></div>
	                    <ul id="nav" class="nav">
	                    <div class="css-wu6zme">
	                       
	                        <li class="nLi">
	                            <div class="css-1ql2hru" style="padding: 0px 5px;">
	                                <?php if(LANG_SET=='zh-cn'){?>
		                        	<div class="css-1smf7ma fch">中文简体&nbsp;&nbsp;</div>
		                        	<?php }elseif(LANG_SET=='en-us'){?>
		                        	<div class="css-1smf7ma fch">English&nbsp;&nbsp;</div>	
		                        	<?php }elseif(LANG_SET=='fr-fr'){?>
		                        	<div class="css-1smf7ma fch">Français&nbsp;&nbsp;</div>	
		                        	<?php }elseif(LANG_SET=='de-de'){?>
		                        	<div class="css-1smf7ma fch">Deutsch&nbsp;&nbsp;</div>	
		                        	<?php }elseif(LANG_SET=='it-it'){?>
		                        	<div class="css-1smf7ma fch">Italiano&nbsp;&nbsp;</div>	
		                        	<?php }elseif(LANG_SET=='ja-jp'){?>
                                    <div class="css-1smf7ma fch">日本語&nbsp;&nbsp;</div>	
		                        	<?php }?>
	                             </div>
	                            <ul class="sub" style="padding:0px;display:none;">
	                               <div class="order_navlist" style="background-color:#f5f5f5;min-width:160px;transform: translate(1161px, 64px);z-index: 99999;">
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=zh-cn')}" style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">简体中文</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=en-us')}"  style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">English</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=fr-fr')}"  style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">Français</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=de-de')}"  style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">Deutsch</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=ja-jp')}"  style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">日本語</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                                   <li  style="list-style:none;">
	                                       <a href="{:U('Login/index?Lang=it-it')}"  style="text-decoration: none;color:#000;">
	                                       <div class="optionli">
	                                           <span  style="color:#000;font-size:14px;">Italiano</span>
	                                       </div>
	                                       </a>
	                                   </li>
	                              </div>
	                            </li>
	                        </div>
	                    </ul>
                    </header>
                    
                    <main clss="main css-1ka9xaf">
                        <div class="css-8t6vxp">
                            <div class="binance-row css-16kn2us" style="margin-left:-12px;margin-right:-12px">
                                <div style="padding-left:12px;padding-right:12px" class="binance-col binance-col-4 css-vurnku"></div>
                                <div  class="binance-col css-1wz0uwi" style="padding-left:12px;padding-right:12px;flex:auto">
                                    <div class="css-9vx3s2">
                                        <div data-bn-type="text" class="css-1g5tc38">{:L('重置密码')}</div>
                                        <div data-bn-type="text" class="css-152kxht">{:L('使用您的电子邮件找回密码')}</div>
                                    </div>
                                    <div class="css-128p7pv">
                                        <div class="css-tmpver">
                                            
                                                <div class="css-hlfj64">
                                                    <div class="css-15651n7">
                                                        <div class="css-xjlny9">{:L('邮箱')}</div>
                                                        <div class="css-hiy16i">
                                                            <div class="css-13xytqh">
                                                                <input data-bn-type="input"  autocomplete="section-email email" type="email" name="email" id="email" class="css-16fg16t" value="">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="css-15651n7">
                                                        <div class="css-xjlny9">{:L('图形验证码')}</div>
                                                        <div class="css-hiy16i" style="height: 48px;">
                                                            <div class="css-13xytqh" style="width:60%;float:left;">
                                                                <input  type="text" id="vcode" name="vcode" class="css-16fg16t" value="">
                                                            </div>
                                                            <div class="css-13xytqh" style="width:30%;float:right;">
                                                                <img style="width:100%;height:50px;border-radius:5px;;cursor:pointer;" src="{:U('Verify/code')}" onclick="this.src=this.src+'?t='+Math.random()" title="{:L('换一张')}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="css-15651n7" style="margin-top:20px;">
                                                        <div class="css-xjlny9">{:L('邮箱验证码')}</div>
                                                        <div class="css-hiy16i" style="height: 48px;">
                                                            <div class="css-13xytqh" style="width:60%;float:left;">
                                                                <input data-bn-type="input"  type="text" id="ecode" name="ecode" class="css-16fg16t" value=""   />
                                                            </div>
                                                            <div class="css-13xytqh" onclick="emailsend();" id="sendsms" style="width:30%;float:right;text-align:center;display:block;line-height:48px;cursor:pointer;">
                                                                <span class="f12 fcy" id="smsstr">{:L('获取验证码')}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="css-15651n7">
                                                        <div class="css-xjlny9">{:L('新密码')}</div>
                                                        <div class="css-hiy16i">
                                                            <div class="css-13xytqh">
                                                                <input data-bn-type="input"  autocomplete="section-mobile current-password" type="password" name="lpwd" id="lpwd" class="css-16fg16t" value="">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button data-bn-type="button"  style="clear:both;margin-top:20px;" class="css-1tsxga3" onclick="resetpwd()">{:L('登录')}</button>
                                        </div>
                                    </div>
                                    
                                </div>
                                <div style="width:100%;height:50px;"></div>
                            </div>
                            
                        </div>
                        
                    </main>
                    
                    
                </div>
            </div>
        </div>
        
    </body>
    <script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>
     <script type="text/javascript">
        function resetpwd(){
            var email = $("#email").val();
            var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if(email=='' || email == null){
                layer.msg("{:L('请输入邮箱')}");return false;       
            }
            if(!reg.test(email)){
                layer.msg("{:L('邮箱格式不正确')}");return false; 
            }
            var ecode = $("#ecode").val();
            if(ecode == ''){
                layer.msg("{:L('请输入邮箱验证码')}");return false; 
            }
            var lpwd = $("#lpwd").val();
            if(lpwd == ''){
                layer.msg("{:L('请输入密码')}");return false; 
            }
            $.post("{:U('Login/resetpwd')}",
            {'email':email,'ecode':ecode,'lpwd':lpwd},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.href = "{:U('Login/index')}";
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            });
        }
</script>
   
    <script type="text/javascript">
        function emailsend(){
            var email = $("#email").val();
            var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if(email=='' || email == null){
                layer.msg("{:L('请输入邮箱')}");return false;       
            }
            if(!reg.test(email)){
                layer.msg("{:L('邮箱格式不正确')}");return false; 
            }
            var vcode = $("#vcode").val();
            if(vcode == ''){
                layer.msg("{:L('请输入图形验证码')}");return false; 
            }
            $.post("{:U('Login/findsendcode')}",
            {'email':email,'vcode':vcode},
            function(data){
                if(data.code == 1){
                    layer.msg(data.info);
                    var obj = $("#sendsms");
                    var strobj = $("#smsstr");
                    var t = 60;
                    var interval = setInterval(function() {
                        obj.removeAttr('onclick');
                        strobj.text(t + "{:L('秒后再发送')}");
                        t--;
                        if(t < 1){
                            obj.attr("onclick","emailsend();");
                            clearInterval(interval);
                            strobj.text("{:L('获取验证码')}");
                        }
                    },1000);
                }else{
                    layer.msg(data.info);
                    $("#sendsms").attr("onclick","emailsend();");
                    $("#smsstr").text("{:L('获取验证码')}");
                    $("#verifycode").click();
                }
            });
        }
    </script>

    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>
    
</html>