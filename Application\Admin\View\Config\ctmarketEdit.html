<include file="Public:header"/>
<style>
    .item-note{color:red!important;font-size:14px!important;}
    .input-10x{width:200px;}
</style>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('Config/ctmarket')}">市场配置</a> &gt;&gt;</span>
            <span class="h1-title"><empty name="data">新增市场配置<else/>编辑市场配置</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('Config/ctmarketEdit')}" method="post" class="form-horizontal" enctype="multipart/form-data">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>

								<tr class="controls" >
									<td class="item-label" style="font-size:14px;">币种名称:</td>
									<td>
										<input type="text" class="form-control input-10x" name="coinname" value="{$data.coinname}">
									</td>
									<td class="item-note" style="font-size:14px;">如：BTC</td>
								</tr>
								

								<tr class="controls">
									<td class="item-label" style="font-size:14px;">K线显示 :</td>
									<td>
									    <select name="status" class="form-control input-10x">
										    <option value="1"<eq name="data.status" value="1">selected</eq>>正常</option>
										    <option value="2"<eq name="data.status" value="2">selected</eq>>禁止</option>
									    </select>
									</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label" style="font-size:14px;">开启交易 :</td>
									<td>
									    <select name="state" class="form-control input-10x">
										    <option value="1"<eq name="data.state" value="1">selected</eq>>正常</option>
										    <option value="2"<eq name="data.state" value="2">selected</eq>>禁止</option>
									    </select>
									</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label" style="font-size:14px;">排序:</td>
									<td>
										<input type="text" class="form-control input-10x" name="sort" value="{$data.sort}">
									</td>
								</tr>

								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">提交
											</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
				<script type="text/javascript">
				//提交表单
				$('#submit').click(function () {
					$('#form').submit();
				});
				</script>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Config/ctmarket')}");
	</script>
</block>