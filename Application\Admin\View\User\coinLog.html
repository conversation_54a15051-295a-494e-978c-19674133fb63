<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">财产统计</span>
        </div>
        <div class="tab-wrap">
            <div class="tab-content">
                <form id="form" class="form-horizontal">
                    <div id="tab" class="tab-pane in tab">
                        <div class="form-item cf">
                            <table>
                                <tr class="controls">
                                    <td class="item-label">用户id :</td>
                                    <td>{$data['userid']}</td>
                                    <td class="item-note"></td>
                                </tr>
                                <tr class="controls">
                                    <td class="item-label">用户名 :</td>
                                    <td>{$data['username']} </td>
                                    <td class="item-note"></td>
                                </tr>
                                <tr class="controls">
                                    <td class="item-label">统计币种 :</td>
                                    <td>{$data['coinname']}</td>
                                    <td class="item-note"></td>
                                </tr>
                                <tr class="controls">
                                    <td class="item-label"></td>
                                    <td></td>
                                    <td class="item-note"> </td>
                                </tr>


                                <tr class="controls">
                                    <td class="item-label">正常余额 :</td>
                                    <td>{$data['zhengcheng']}</td>
                                    <td class="item-note">用户账户里面实际余额</td>
                                </tr>
                                <tr class="controls">
                                    <td class="item-label">冻结余额 :</td>
                                    <td>{$data['dongjie']}</td>
                                    <td class="item-note">用户账户里面实际余额</td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label">总计余额 :</td>
                                    <td>{$data['zongji']}</td>
                                    <td class="item-note">  用户账户里面实际余额</td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label"></td>
                                    <td></td>
                                    <td class="item-note"> </td>
                                </tr>
                                <tr class="controls">
                                    <td class="item-label">累计转入虚拟币 :</td>
                                    <td>{$data['chongzhi']}</td>
                                    <td class="item-note">用户转入到网站的</td>
                                </tr>


                                <tr class="controls">
                                    <td class="item-label">累计转出虚拟币 :</td>
                                    <td>{$data['tixian']}</td>
                                    <td class="item-note">用户从网站转出的</td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label"></td>
                                    <td></td>
                                    <td class="item-note">以上数据仅供参考 </td>
                                </tr>


                            </table>
                        </div>
                    </div>
                </form>
                <script type="text/javascript">
                    //提交表单
                    $('#submit').click(function () {
                        $('#form').submit();
                    });
                </script>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    //搜索功能
    $(function () {
        $('#search').click(function () {
            $('#formSearch').submit();
        });
    });
    //回车搜索
    $(".search-input").keyup(function (e) {
        if (e.keyCode === 13) {
            $("#search").click();
            return false;
        }
    });
</script>

<include file="Public:footer"/>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('User/coin')}");
    </script>
</block>