<include file="Public:header"/>
<script type="text/javascript" src="__PUBLIC__/layer/laydate/laydate.js"></script>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('Article/index')}">公告管理</a> >></span>
            <span class="h1-title"><empty name="data">添加公告<else/>编辑公告</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('Article/ggeditup')}" method="post" class="form-horizontal" enctype="multipart/form-data">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>
								<tr class="controls">
									<td class="item-label">公告标题：</td>
									<td>
										<input type="text" class="form-control input-10x" name="title" value="{$data.title}">
									</td>
									<td class="item-note"></td>
								</tr>

								<tr class="controls" >
									<td class="item-label">缩略图片 :</td>
									<td>
										<div id="addpicContainer">
											<notempty name="data.img">
												<!--没有图片显示默认图片-->
												<img id="up_img" onclick="getElementById('inputfile').click()" style="cursor:pointer;max-width:100px;" title="点击添加图片" alt="点击添加图片" src="/Upload/article/{$data.img}">
												<else/>
												<!--没有图片显示默认图片-->
												<img id="up_img" onclick="getElementById('inputfile').click()" style="cursor:pointer;max-width:100px;" title="点击添加图片" alt="点击添加图片" src="__PUBLIC__/Admin/images/addimg.png">
											</notempty>
											<input type="hidden" id="img" name="img" value="{$data.img}">
											<input type="file" id="inputfile" style="height:0;width:0;z-index: -1; position: absolute;left: 10px;top: 5px;" value="/Upload/wenzhang/{$data.img}"/>
										</div>
									</td>
									<td class="item-note"></td>
								</tr>
                                
                                <br /><br />
								<tr class="controls" style="margin-top:30px;">
									<td class="item-label">公告内容 :</td>
									<td>
										<textarea name="content" class="form-control input-10x">{$data.content}</textarea>
									</td>
									<td class="item-note"></td>
								</tr>


								<tr class="controls">
									<td class="item-label">状态 :</td>
									<td>
										<select name="status" class="form-control input-10x">
											<option value="1" <eq name="data.status" value="1">selected</eq>>显示</option>
											<option value="2" <eq name="data.status" value="2">selected</eq>>隐藏</option>
										</select>
									</td>
									<td class="item-note"></td>
								</tr>
								<input type="hidden"  name="id" value="{$data.id}" />
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="ajax-post btn submit-btn" id="submit" type="submit" target-form="form-horizontal">提交</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
				<script type="text/javascript">
					//提交表单
					$('#submit').click(function () {
						$('#form').submit();
					});
				</script>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript" src="__PUBLIC__/kindeditor/kindeditor-min.js"></script>
<script type="text/javascript">
	var editor;
	KindEditor.ready(function (K) {
		editor = K.create('textarea[name="content"]', {
			width: '800px',
			height: '300px',
			allowPreviewEmoticons: false,
			allowImageUpload: true,
			uploadJson: "{:U('Article/edit','type=images')}",//图片上传后的处理地址
			items: [
				'source', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',
				'removeformat', '|', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',
				'insertunorderedlist', '|', 'emoticons', 'image', 'link', 'fullscreen'],
			afterBlur: function () {
				this.sync();
			}
		});
	});
</script>
<script type="text/javascript">
    /** 手机端网站logo上传 **/
	$(document).ready(function () {
		//响应文件添加成功事件
		$("#inputfile").change(function () {
			//创建FormData对象
			var data = new FormData();
			//为FormData对象添加数据
			$.each($('#inputfile')[0].files, function (i, file) {
				data.append('upload_file' + i, file);
			});
			//发送数据
			$.ajax({
				url: '/Admin/Article/wenzhangimg',
				type: 'POST',
				data: data,
				cache: false,
				contentType: false,		//不可缺参数
				processData: false,		//不可缺参数
				success: function (data) {
					if (data) {
						$('#up_img').attr("src", '/Upload/article/' + $.trim(data));
						$('#img').val($.trim(data));
						$('#up_img').show();
					}
				},
				error: function () {
					alert('上传出错');
					$(".loading").hide();	//加载失败移除加载图片
				}
			});

		});
	});
</script>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Article/index')}");
	</script>
</block>