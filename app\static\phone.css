html {
    font-size: 62.5%;
    position: relative;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%
}

/*互动图片消失*/
/**:not(html,.layui-m-layer1,.layui-m-layer1 div){*/
/*-webkit-transform:translate3d(0,0,0);*/
/*transform:translate3d(0,0,0)*/
/*}*/

body {
    width: 100%;
    height: 100%;

    margin: 0 auto;
    padding: 0;
    font-size: 14px;
    color: #333;
    font-family: "MicroSoft YaHei";
    position: relative;
    z-index: 5;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    background-color: #f2f2f2;
}

.i2 {
    width: 100%;
    background: url(./BG.png) no-repeat top center;
    background-size: 100% 100%;
    height: 11.6rem;
}

.i2 .self .r1 {
    width: 100%;
}

.i2 .self div {
    position: absolute;
}

.i2 .self .r1 .title {
    top: 0.69rem;
    width: 100%;
}

.i2 .self .r1 .title img {
    width: 2.86rem;
    height: 0.68rem;
    display: block;
    margin: 0 auto;
}

.i2 .self .l1 {
    width: 100%;
    top: 1.67rem;
}

.i2 .self .l1 img {
    display: block;
    width: 2.6rem;
    height: 5rem;
    margin: 0 auto;
}

.i2 .self .r1 .right {
    top: 5.8rem;
    width: 100%;
}

.i2 .self .r1 .right .right-btn {
    width: 100%;
}

.i2 .self .r1 .right .right-btn .btn {

    width: 100%;
    height: 100%;
}

.i2 .self .r1 .right .right-btn .btn img {
    width: 1.5rem;
    height: 1.84rem;
    margin: 0 auto;
    display: block;
}

.i2 .self .r1 .left-btn {
    width: 100%;
    position: relative;
    top: 4rem;
}

.i2 .self .r1 .left-btn .btn {
    width: 100%;
}

.i2 .self .r1 .left-btn .btn img {
    width: 2.3rem;
    height: 0.61rem;
    display: block;
    margin: 0 auto;
}

.i2 .self .r1 .left-btn .btn:nth-child(2) {
    position: relative;
    top: 0.2rem;
}

.i3 {
    height: 0.9rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: #968080;
}

.i3 .item {
    text-align: center;
    font-size: 0.12rem;
    color: #ffffff;
    /*line-height: 0.18rem;*/
}

.i3 .item:nth-child(2) {
    display: flex;
    flex-direction: column;
    font-size: 0.1rem;
    color: #ffffff;
    margin-top: 0.1rem;
}


.imgs {
    background: url('http://images-7n.365tggj.com/gw_live_weixin.png') no-repeat;
    background-size: 100% 110%;
    z-index: 999999;
    min-height: 100%;
    /* background-size:contain;*/
    width: 100%;
}

.wxtip {
    background: rgba(0, 0, 0, 0.8);
    text-align: center;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 998;
    display: none
}

.wxtip.wxtip_show {
    display: block;
}