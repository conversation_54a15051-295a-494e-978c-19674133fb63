<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">编辑财产</span>
            <notempty name="name">
                <span class="h2-title">>><a href="{:U('User/coin')}">财产列表</a></span>
            </notempty>
        </div>

        <div class="tab-wrap">

            <div class="tab-content">
                <form id="form" action="{:U('User/coinEdit')}" method="post" class="form-horizontal">
                    <div id="tab" class="tab-pane in tab">
                        <div class="form-item cf">
                            <table>

                                <tr class="controls">
                                    <td class="item-label">用户id :</td>

                                    <td>{$data.userid}</td>
                                    <td class="item-note"></td>

                                </tr>

                                <volist name=":C('coin')" id="v">
                                    <tr class="controls">
                                        <td class="item-label">可用{$v['title']} :</td>

                                        <td><input type="text" class="form-control input-10x" name="{$v['name']}"
                                                   value="{$data[$v['name']]*1}"></td>
                                        <td class="item-note">请谨慎修改</td>

                                    </tr>
                                </volist>

                                <tr class="controls">
                                    <td class="item-label"></td>
                                    <td>
                                        <div class="form-item cf">
                                            <button class="btn submit-btn ajax-post" id="submit" type="submit"
                                                    target-form="form-horizontal">提交
                                            </button>
                                            <a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
                                            <notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
                                            </notempty>
                                        </div>
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    //提交表单
    $('#submit').click(function () {
        $('#form').submit();
    });
</script>

<include file="Public:footer"/>
<block name="script">
    <script type="text/javascript" charset="utf-8">
        //导航高亮
        highlight_subnav("{:U('User/coin')}");
    </script>
</block>